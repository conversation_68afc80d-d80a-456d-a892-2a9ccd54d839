globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/subscribed/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/components/Index.js":{"*":{"id":"(ssr)/./src/app/components/Index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/ClerkAuthSync.js":{"*":{"id":"(ssr)/./src/app/components/ClerkAuthSync.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/home/<USER>":{"*":{"id":"(ssr)/./src/app/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/match/page.js":{"*":{"id":"(ssr)/./src/app/match/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/chat/page.js":{"*":{"id":"(ssr)/./src/app/chat/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/all_chats/page.js":{"*":{"id":"(ssr)/./src/app/all_chats/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/pricing/page.js":{"*":{"id":"(ssr)/./src/app/pricing/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/subscribed/page.js":{"*":{"id":"(ssr)/./src/app/subscribed/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/companion/[id]/page.js":{"*":{"id":"(ssr)/./src/app/companion/[id]/page.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\components\\Index.js":{"id":"(app-pages-browser)/./src/app/components/Index.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\ClerkProvider.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\keyless-cookie-sync.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\PromisifiedAuthProvider.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\components\\ClerkAuthSync.js":{"id":"(app-pages-browser)/./src/app/components/ClerkAuthSync.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\home\\page.js":{"id":"(app-pages-browser)/./src/app/home/<USER>","name":"*","chunks":[],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\match\\page.js":{"id":"(app-pages-browser)/./src/app/match/page.js","name":"*","chunks":[],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\chat\\page.js":{"id":"(app-pages-browser)/./src/app/chat/page.js","name":"*","chunks":[],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\all_chats\\page.js":{"id":"(app-pages-browser)/./src/app/all_chats/page.js","name":"*","chunks":[],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\pricing\\page.js":{"id":"(app-pages-browser)/./src/app/pricing/page.js","name":"*","chunks":[],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\subscribed\\page.js":{"id":"(app-pages-browser)/./src/app/subscribed/page.js","name":"*","chunks":["app/subscribed/page","static/chunks/app/subscribed/page.js"],"async":false},"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\companion\\[id]\\page.js":{"id":"(app-pages-browser)/./src/app/companion/[id]/page.js","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\":[],"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\page":[],"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\layout":["static/css/app/layout.css"],"D:\\Fabaf Projects\\MERN\\pinkhoney-main\\frontend\\src\\app\\subscribed\\page":[]}}