{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"]}, "jsx": "react-jsx", "module": "esnext", "moduleResolution": "node", "target": "es2015", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true}, "include": ["**/*.js", "**/*.jsx"], "exclude": ["node_modules"]}