// components/SwipeCard.js
"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, <PERSON>, <PERSON> } from "lucide-react";

const profiles = [
  { id: 1, name: "<PERSON>", image: "/avatar.PNG" },
  { id: 2, name: "<PERSON>", image: "/avatar2.PNG" },
  { id: 3, name: "<PERSON>", image: "/avatar3.PNG" },
  { id: 3, name: "<PERSON>", image: "/avatar3.PNG" },
  { id: 3, name: "<PERSON>", image: "/avatar3.PNG" },
  { id: 3, name: "<PERSON>", image: "/avatar3.PNG" },
  { id: 3, name: "<PERSON>", image: "/avatar3.PNG" },
  { id: 3, name: "<PERSON>", image: "/avatar3.PNG" },
  { id: 3, name: "<PERSON>", image: "/avatar3.PNG" },
  { id: 3, name: "<PERSON>", image: "/avatar3.PNG" },
  { id: 3, name: "<PERSON>", image: "/avatar3.PNG" },
  { id: 3, name: "<PERSON>", image: "/avatar3.PNG" },

  // Add more profiles as needed
];

export default function SwipeCard() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(null);

  const currentProfile = profiles[currentIndex];

  const swipe = (direction) => {
    setDirection(direction);
    setTimeout(() => {
      setDirection(null);
      setCurrentIndex((prev) => (prev + 1) % profiles.length);
    }, 300);
  };

  return (
    <div className="max-w-sm mx-auto relative h-[600px]">
      <AnimatePresence>
        <motion.div
          key={currentProfile.id}
          initial={
            direction
              ? {
                x:
                  direction === "right"
                    ? -300
                    : direction === "left"
                      ? 300
                      : 0,
              }
              : false
          }
          animate={{ x: 0, rotate: 0 }}
          exit={{
            x: direction === "right" ? 300 : direction === "left" ? -300 : 0,
            rotate: direction === "right" ? 20 : direction === "left" ? -20 : 0,
          }}
          transition={{ duration: 0.3 }}
          className="absolute w-full"
        >
          {/* Card Container */}
          <div className="relative rounded-2xl overflow-hidden shadow-xl">
            {/* Image */}
            <img
              src={`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8080'}${currentProfile.image}`}
              alt="Profile"
              className="w-full h-[600px] object-cover"
            />

            {/* Gradient Overlay */}
            <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/60 to-transparent">
              {/* Name */}
              <div className="absolute bottom-20 left-4">
                <h2 className="text-white text-2xl font-semibold">
                  {currentProfile.name}
                </h2>
              </div>

              {/* Action Buttons */}
              <div className="absolute bottom-4 left-0 right-0 flex justify-center items-center gap-6">
                <button
                  onClick={() => swipe("left")}
                  className="w-14 h-14 flex items-center justify-center rounded-full bg-white shadow-lg hover:bg-gray-100 transition-colors"
                >
                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 32 32"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M16 27.6667C13.4373 27.6632 10.9806 26.6436 9.16851 24.8315C7.35642 23.0194 6.33684 20.5627 6.33331 18C6.33331 17.7348 6.43867 17.4804 6.62621 17.2929C6.81374 17.1054 7.0681 17 7.33331 17C7.59853 17 7.85288 17.1054 8.04042 17.2929C8.22796 17.4804 8.33331 17.7348 8.33331 18C8.33331 19.5163 8.78296 20.9986 9.62538 22.2594C10.4678 23.5202 11.6652 24.5028 13.0661 25.0831C14.467 25.6634 16.0085 25.8152 17.4957 25.5194C18.9829 25.2235 20.3489 24.4934 21.4211 23.4212C22.4933 22.349 23.2235 20.9829 23.5193 19.4957C23.8152 18.0085 23.6633 16.467 23.0831 15.0661C22.5028 13.6652 21.5201 12.4678 20.2594 11.6254C18.9986 10.783 17.5163 10.3333 16 10.3333H12.6666C12.4014 10.3333 12.1471 10.228 11.9595 10.0405C11.772 9.85291 11.6666 9.59856 11.6666 9.33334C11.6666 9.06813 11.772 8.81377 11.9595 8.62624C12.1471 8.4387 12.4014 8.33334 12.6666 8.33334H16C18.5637 8.33334 21.0225 9.35179 22.8353 11.1646C24.6482 12.9775 25.6666 15.4363 25.6666 18C25.6666 20.5638 24.6482 23.0225 22.8353 24.8354C21.0225 26.6482 18.5637 27.6667 16 27.6667Z"
                      fill="#FE506B"
                    />
                    <path
                      d="M16 14.3333C15.8686 14.3339 15.7384 14.3083 15.6171 14.2579C15.4957 14.2076 15.3856 14.1335 15.2933 14.04L11.2933 10.04C11.106 9.85249 11.0009 9.59833 11.0009 9.33332C11.0009 9.06832 11.106 8.81416 11.2933 8.62666L15.2933 4.62666C15.3849 4.52841 15.4953 4.44961 15.6179 4.39495C15.7406 4.34029 15.873 4.3109 16.0073 4.30854C16.1415 4.30617 16.2749 4.33087 16.3994 4.38116C16.524 4.43146 16.6371 4.50632 16.732 4.60127C16.827 4.69623 16.9018 4.80934 16.9521 4.93386C17.0024 5.05838 17.0271 5.19175 17.0248 5.32602C17.0224 5.46029 16.993 5.59271 16.9383 5.71538C16.8837 5.83804 16.8049 5.94844 16.7066 6.03999L13.4133 9.33332L16.7066 12.6267C16.8939 12.8142 16.9991 13.0683 16.9991 13.3333C16.9991 13.5983 16.8939 13.8525 16.7066 14.04C16.6143 14.1335 16.5042 14.2076 16.3829 14.2579C16.2615 14.3083 16.1314 14.3339 16 14.3333Z"
                      fill="#FE506B"
                    />
                  </svg>
                </button>
                {/* Dislike Button */}
                <button
                  onClick={() => swipe("left")}
                  className="w-14 h-14 flex items-center justify-center rounded-full bg-white shadow-lg hover:bg-gray-100 transition-colors"
                >
                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 32 32"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      clip-rule="evenodd"
                      d="M16.7907 31.0107L16.776 31.0133L16.6813 31.06L16.6547 31.0653L16.636 31.06L16.5413 31.0133C16.5271 31.0089 16.5165 31.0111 16.5093 31.02L16.504 31.0333L16.4813 31.604L16.488 31.6307L16.5013 31.648L16.64 31.7467L16.66 31.752L16.676 31.7467L16.8147 31.648L16.8307 31.6267L16.836 31.604L16.8133 31.0347C16.8098 31.0204 16.8022 31.0124 16.7907 31.0107ZM17.144 30.86L17.1267 30.8627L16.88 30.9867L16.8667 31L16.8627 31.0147L16.8867 31.588L16.8933 31.604L16.904 31.6133L17.172 31.7373C17.1889 31.7418 17.2018 31.7382 17.2107 31.7267L17.216 31.708L17.1707 30.8893C17.1662 30.8733 17.1573 30.8635 17.144 30.86ZM16.1907 30.8627C16.1848 30.8591 16.1778 30.8579 16.1711 30.8594C16.1644 30.8609 16.1585 30.8649 16.1547 30.8707L16.1467 30.8893L16.1013 31.708C16.1022 31.724 16.1098 31.7347 16.124 31.74L16.144 31.7373L16.412 31.6133L16.4253 31.6027L16.4307 31.588L16.4533 31.0147L16.4493 30.9987L16.436 30.9853L16.1907 30.8627Z"
                      fill="#FE506B"
                    />
                    <path
                      fillRule="evenodd"
                      clip-rule="evenodd"
                      d="M16 18.8293L23.0707 25.9C23.4459 26.2752 23.9547 26.486 24.4853 26.486C25.0159 26.486 25.5248 26.2752 25.9 25.9C26.2752 25.5248 26.486 25.0159 26.486 24.4853C26.486 23.9547 26.2752 23.4459 25.9 23.0707L18.8267 16L25.8987 8.92933C26.0844 8.74355 26.2316 8.52302 26.3321 8.28032C26.4326 8.03763 26.4842 7.77752 26.4842 7.51486C26.4841 7.25219 26.4323 6.99211 26.3317 6.74946C26.2312 6.50681 26.0838 6.28635 25.898 6.10066C25.7122 5.91497 25.4917 5.76769 25.249 5.66723C25.0063 5.56677 24.7462 5.5151 24.4835 5.51516C24.2209 5.51522 23.9608 5.56702 23.7181 5.66759C23.4755 5.76817 23.255 5.91555 23.0693 6.10133L16 13.172L8.92933 6.10133C8.74493 5.91022 8.52431 5.75776 8.28035 5.65282C8.0364 5.54789 7.77398 5.4926 7.50843 5.49017C7.24287 5.48773 6.97949 5.53821 6.73365 5.63866C6.48781 5.7391 6.26444 5.88751 6.07657 6.0752C5.88869 6.2629 5.74008 6.48613 5.6394 6.73188C5.53873 6.97762 5.488 7.24095 5.49018 7.50651C5.49236 7.77207 5.54741 8.03453 5.65211 8.27859C5.75681 8.52265 5.90907 8.74341 6.1 8.92799L13.1733 16L6.10133 23.072C5.9104 23.2566 5.75815 23.4773 5.65344 23.7214C5.54874 23.9655 5.49369 24.2279 5.49151 24.4935C5.48933 24.759 5.54006 25.0224 5.64074 25.2681C5.74141 25.5139 5.89003 25.7371 6.0779 25.9248C6.26577 26.1125 6.48914 26.2609 6.73498 26.3613C6.98082 26.4618 7.2442 26.5123 7.50976 26.5098C7.77532 26.5074 8.03773 26.4521 8.28169 26.3472C8.52565 26.2422 8.74626 26.0898 8.93067 25.8987L16 18.8293Z"
                      fill="#FE506B"
                    />
                  </svg>

                  {/* <X className="w-8 h-8 text-gray-600" /> */}
                </button>

                {/* Like Button */}
                <button
                  onClick={() => swipe("right")}
                  className="w-14 h-14 flex items-center justify-center rounded-full bg-brand-pink shadow-lg hover:bg-gray-100 transition-colors"
                >
                  <svg
                    width="44"
                    height="44"
                    viewBox="0 0 44 44"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M22 39.1417L19.3417 36.7217C9.89999 28.16 3.66666 22.495 3.66666 15.5833C3.66666 9.91833 8.10332 5.5 13.75 5.5C16.94 5.5 20.0017 6.985 22 9.31333C23.9983 6.985 27.06 5.5 30.25 5.5C35.8967 5.5 40.3333 9.91833 40.3333 15.5833C40.3333 22.495 34.1 28.16 24.6583 36.7217L22 39.1417Z"
                      fill="white"
                    />
                  </svg>

                  {/* <Heart className="w-8 h-8 text-white" /> */}
                </button>

                {/* Favorite Button */}
                <button
                  onClick={() => swipe("up")}
                  className="w-14 h-14 flex items-center justify-center rounded-full bg-white shadow-lg hover:bg-gray-100 transition-colors"
                >
                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 32 32"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.9907 9.78666L2.48399 11.02L2.33333 11.0507C2.10525 11.1112 1.89732 11.2312 1.73078 11.3984C1.56425 11.5656 1.44506 11.774 1.38541 12.0023C1.32575 12.2306 1.32776 12.4707 1.39123 12.698C1.4547 12.9252 1.57735 13.1316 1.74666 13.296L7.90933 19.2947L6.45599 27.768L6.43866 27.9147C6.4247 28.1506 6.47368 28.3859 6.58059 28.5967C6.68751 28.8074 6.84851 28.9859 7.04711 29.114C7.24571 29.2421 7.47478 29.315 7.71086 29.3255C7.94694 29.3359 8.18155 29.2834 8.39066 29.1733L15.9987 25.1733L23.5893 29.1733L23.7227 29.2347C23.9427 29.3213 24.1819 29.3479 24.4157 29.3117C24.6494 29.2754 24.8693 29.1776 25.0528 29.0283C25.2363 28.8791 25.3767 28.6837 25.4598 28.4622C25.5428 28.2407 25.5654 28.0011 25.5253 27.768L24.0707 19.2947L30.236 13.2947L30.34 13.1813C30.4886 12.9983 30.586 12.7793 30.6223 12.5464C30.6586 12.3135 30.6326 12.0752 30.5468 11.8556C30.461 11.6361 30.3186 11.4432 30.134 11.2967C29.9494 11.1501 29.7293 11.0551 29.496 11.0213L20.9893 9.78666L17.1867 2.07999C17.0766 1.8567 16.9063 1.66867 16.6949 1.53719C16.4835 1.40571 16.2396 1.33603 15.9907 1.33603C15.7417 1.33603 15.4978 1.40571 15.2864 1.53719C15.075 1.66867 14.9047 1.8567 14.7947 2.07999L10.9907 9.78666Z"
                      fill="#672653"
                    />
                  </svg>

                  {/* <Star className="w-8 h-8 text-purple-500" /> */}
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
