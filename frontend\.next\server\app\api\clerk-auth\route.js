"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/clerk-auth/route";
exports.ids = ["app/api/clerk-auth/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fclerk-auth%2Froute&page=%2Fapi%2Fclerk-auth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclerk-auth%2Froute.js&appDir=D%3A%5CFabaf%20Projects%5CMERN%5Cpinkhoney-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CFabaf%20Projects%5CMERN%5Cpinkhoney-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fclerk-auth%2Froute&page=%2Fapi%2Fclerk-auth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclerk-auth%2Froute.js&appDir=D%3A%5CFabaf%20Projects%5CMERN%5Cpinkhoney-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CFabaf%20Projects%5CMERN%5Cpinkhoney-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Fabaf_Projects_MERN_pinkhoney_main_frontend_src_app_api_clerk_auth_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/clerk-auth/route.js */ \"(rsc)/./src/app/api/clerk-auth/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/clerk-auth/route\",\n        pathname: \"/api/clerk-auth\",\n        filename: \"route\",\n        bundlePath: \"app/api/clerk-auth/route\"\n    },\n    resolvedPagePath: \"D:\\\\Fabaf Projects\\\\MERN\\\\pinkhoney-main\\\\frontend\\\\src\\\\app\\\\api\\\\clerk-auth\\\\route.js\",\n    nextConfigOutput,\n    userland: D_Fabaf_Projects_MERN_pinkhoney_main_frontend_src_app_api_clerk_auth_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/clerk-auth/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fclerk-auth%2Froute&page=%2Fapi%2Fclerk-auth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclerk-auth%2Froute.js&appDir=D%3A%5CFabaf%20Projects%5CMERN%5Cpinkhoney-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CFabaf%20Projects%5CMERN%5Cpinkhoney-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/clerk-auth/route.js":
/*!*****************************************!*\
  !*** ./src/app/api/clerk-auth/route.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/api */ \"(rsc)/./src/services/api.js\");\n\n\nasync function POST(request) {\n    try {\n        const { userId, email, firstName, lastName } = await request.json();\n        if (!userId || !email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        // Call your backend API to create/update user in MongoDB\n        const response = await fetch((0,_services_api__WEBPACK_IMPORTED_MODULE_1__.getApiUrl)(\"api/clerk_sync\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email,\n                clerkId: userId,\n                firstName,\n                lastName\n            })\n        });\n        if (!response.ok) {\n            let errorMessage = \"Failed to sync user with database\";\n            try {\n                const contentType = response.headers.get(\"content-type\");\n                if (contentType && contentType.includes(\"application/json\")) {\n                    const errorData = await response.json();\n                    errorMessage = errorData.error || errorMessage;\n                } else {\n                    // If not JSON, just get the text\n                    const errorText = await response.text();\n                    console.error(\"Non-JSON error response:\", errorText);\n                }\n            } catch (parseError) {\n                console.error(\"Error parsing error response:\", parseError);\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: errorMessage\n            }, {\n                status: response.status\n            });\n        }\n        try {\n            const data = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        } catch (parseError) {\n            console.error(\"Error parsing JSON response:\", parseError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid response from server\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error syncing user with database:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/clerk-auth/route.js\n");

/***/ }),

/***/ "(rsc)/./src/services/api.js":
/*!*****************************!*\
  !*** ./src/services/api.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiDelete: () => (/* binding */ apiDelete),\n/* harmony export */   apiGet: () => (/* binding */ apiGet),\n/* harmony export */   apiPost: () => (/* binding */ apiPost),\n/* harmony export */   apiPut: () => (/* binding */ apiPut),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getBaseUrl: () => (/* binding */ getBaseUrl)\n/* harmony export */ });\n/**\r\n * Centralized API service for making requests to the backend\r\n */ /**\r\n * Get the base API URL from environment variables\r\n * Falls back to localhost if not defined\r\n */ const getBaseUrl = ()=>{\n    return \"http://localhost:8080\" || 0;\n};\n/**\r\n * Get the API endpoint URL by combining base URL with endpoint path\r\n * @param {string} endpoint - API endpoint path (with or without leading slash)\r\n * @returns {string} Full API URL\r\n */ const getApiUrl = (endpoint)=>{\n    const baseUrl = getBaseUrl();\n    const normalizedEndpoint = endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`;\n    return `${baseUrl}${normalizedEndpoint}`;\n};\n/**\r\n * Make a GET request to the API\r\n * @param {string} endpoint - API endpoint path\r\n * @param {Object} options - Fetch options\r\n * @returns {Promise<any>} Response data\r\n */ const apiGet = async (endpoint, options = {})=>{\n    try {\n        const response = await fetch(getApiUrl(endpoint), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...options.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.error || `API error: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(`Error fetching ${endpoint}:`, error);\n        throw error;\n    }\n};\n/**\r\n * Make a POST request to the API\r\n * @param {string} endpoint - API endpoint path\r\n * @param {Object} data - Request body data\r\n * @param {Object} options - Fetch options\r\n * @returns {Promise<any>} Response data\r\n */ const apiPost = async (endpoint, data, options = {})=>{\n    try {\n        const response = await fetch(getApiUrl(endpoint), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...options.headers\n            },\n            body: JSON.stringify(data),\n            ...options\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.error || `API error: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(`Error posting to ${endpoint}:`, error);\n        throw error;\n    }\n};\n/**\r\n * Make a PUT request to the API\r\n * @param {string} endpoint - API endpoint path\r\n * @param {Object} data - Request body data\r\n * @param {Object} options - Fetch options\r\n * @returns {Promise<any>} Response data\r\n */ const apiPut = async (endpoint, data, options = {})=>{\n    try {\n        const response = await fetch(getApiUrl(endpoint), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...options.headers\n            },\n            body: JSON.stringify(data),\n            ...options\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.error || `API error: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(`Error putting to ${endpoint}:`, error);\n        throw error;\n    }\n};\n/**\r\n * Make a DELETE request to the API\r\n * @param {string} endpoint - API endpoint path\r\n * @param {Object} options - Fetch options\r\n * @returns {Promise<any>} Response data\r\n */ const apiDelete = async (endpoint, options = {})=>{\n    try {\n        const response = await fetch(getApiUrl(endpoint), {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...options.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.error || `API error: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(`Error deleting ${endpoint}:`, error);\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/api.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fclerk-auth%2Froute&page=%2Fapi%2Fclerk-auth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclerk-auth%2Froute.js&appDir=D%3A%5CFabaf%20Projects%5CMERN%5Cpinkhoney-main%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CFabaf%20Projects%5CMERN%5Cpinkhoney-main%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();